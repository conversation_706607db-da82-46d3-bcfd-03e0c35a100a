"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils/cn";
import { Button } from "@/app/components/ui/button";
import { LuMenu, LuLayoutDashboard as LuDashboard, LuShoppingCart, LuUsers, LuCreditCard, LuPackage, LuSettings, LuUser } from "react-icons/lu";
import { ScrollArea } from "@/app/components/ui/scroll-area";
import { Sheet, SheetContent, SheetTrigger, SheetTitle } from "@/app/components/ui/sheet";
import { useEffect, useState } from "react";
import { NotificationBadge } from "@/app/components/shared/notification/notification-badge";
import { ThemeToggle } from "@/app/components/ui/theme-toggle";

interface AdminLayoutProps {
  children: React.ReactNode;
}

const navigation = [
  { name: "Dashboard", href: "/admin/dashboard", icon: LuDashboard },
  { name: "<PERSON><PERSON><PERSON>", href: "/admin/users", icon: LuUser<PERSON> },
  { name: "Produk", href: "/admin/products", icon: LuPackage },
  { name: "<PERSON><PERSON><PERSON><PERSON>", href: "/admin/rentals", icon: LuShoppingCart },
  { name: "Pembayaran", href: "/admin/payments", icon: LuCreditCard },
  { name: "Operasional", href: "/admin/operations", icon: LuSettings },
  { name: "Profil", href: "/admin/profile", icon: LuUser },
];

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const [user, setUser] = useState<{ id?: string; name?: string; email?: string; } | null>(null);

  useEffect(() => {
    // Fetch user data from server
    const fetchUserData = async () => {
      try {
        // Use relative URL to automatically handle base path
        const response = await fetch('/api/users/me', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };

    fetchUserData();
  }, []);

  return (
    <div className="flex min-h-screen bg-gray-50/40 dark:bg-gray-900/90">
      {/* Sidebar untuk desktop */}
      <aside className="hidden md:flex md:w-64 md:flex-col">
        <div className="flex flex-col flex-1 min-h-0 border-r bg-white shadow-sm dark:bg-gray-800 dark:border-gray-700">
          <div className="flex items-center h-16 px-6 border-b bg-blue-600 text-white dark:bg-blue-700 dark:border-blue-600">
            <Link href="/admin/dashboard" className="flex items-center">
              <span className="text-xl font-semibold">RentalGenset</span>
            </Link>
          </div>
          <ScrollArea className="flex-1 py-4">
            <nav className="space-y-1 px-2">
              {navigation.map((item) => {
                const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      "group flex items-center px-3 py-2 text-sm font-medium rounded-md",
                      isActive
                        ? "bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                        : "text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-gray-100"
                    )}
                  >
                    <item.icon
                      className={cn(
                        "mr-3 h-5 w-5 flex-shrink-0",
                        isActive
                          ? "text-blue-600 dark:text-blue-400"
                          : "text-gray-500 dark:text-gray-400"
                      )}
                    />
                    {item.name}
                  </Link>
                );
              })}
            </nav>
          </ScrollArea>
          {user && (
            <div className="flex items-center gap-3 p-4 border-t dark:border-gray-700">
              <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-400 font-medium">
                  {user.name?.[0] || "U"}
                </span>
              </div>
              <div className="flex-1 truncate">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{user.name}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">{user.email}</p>
              </div>
              <ThemeToggle />
              <NotificationBadge />
            </div>
          )}
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <Sheet>
        <SheetTrigger>
          <Button variant="ghost" size="icon" className="md:hidden absolute top-4 left-4">
            <LuMenu className="h-6 w-6" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-64">
          <SheetTitle className="sr-only">Menu Navigasi Admin</SheetTitle>
          <div className="flex flex-col min-h-screen">
            <div className="flex items-center h-16 px-6 border-b bg-blue-600 text-white dark:bg-blue-700 dark:border-blue-600">
              <Link href="/admin/dashboard" className="flex items-center">
                <span className="text-xl font-semibold">RentalGenset</span>
              </Link>
            </div>
            <ScrollArea className="flex-1 py-4">
              <nav className="space-y-1 px-2">
                {navigation.map((item) => {
                  const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        "group flex items-center px-3 py-2 text-sm font-medium rounded-md",
                        isActive
                          ? "bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                          : "text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-gray-100"
                      )}
                    >
                      <item.icon
                        className={cn(
                          "mr-3 h-5 w-5 flex-shrink-0",
                          isActive
                            ? "text-blue-600 dark:text-blue-400"
                            : "text-gray-500 dark:text-gray-400"
                        )}
                      />
                      {item.name}
                    </Link>
                  );
                })}
              </nav>
            </ScrollArea>
            {user && (
              <div className="flex items-center gap-3 p-4 border-t dark:border-gray-700">
                <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                  <span className="text-blue-600 dark:text-blue-400 font-medium">
                    {user.name?.[0] || "U"}
                  </span>
                </div>
                <div className="flex-1 truncate">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{user.name}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{user.email}</p>
                </div>
                <ThemeToggle />
                <NotificationBadge />
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <main className="flex-1 overflow-y-auto pb-10">
        <div className="flex items-center justify-end gap-2 p-4 border-b bg-white dark:bg-gray-800 dark:border-gray-700">
          <ThemeToggle />
          <NotificationBadge />
        </div>
        <div className="px-4 sm:px-6 lg:px-8 py-6 max-w-7xl mx-auto">
          {children}
        </div>
      </main>
    </div>
  );
}
