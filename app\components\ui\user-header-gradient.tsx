'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

interface UserHeaderGradientProps {
  children: React.ReactNode;
  className?: string;
}

export function UserHeaderGradient({ children, className = '' }: UserHeaderGradientProps) {
  const { theme, systemTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return with default light gradient during SSR
    return (
      <div 
        className={`${className}`}
        style={{
          background: 'linear-gradient(to right, #7c3aed, #9333ea, #6366f1)'
        }}
      >
        {children}
      </div>
    );
  }

  const currentTheme = theme === 'system' ? systemTheme : theme;
  const isDark = currentTheme === 'dark';

  const gradientStyle = {
    background: isDark 
      ? 'linear-gradient(to right, #4c1d95, #581c87, #3730a3)'
      : 'linear-gradient(to right, #7c3aed, #9333ea, #6366f1)'
  };

  return (
    <div 
      className={`${className}`}
      style={gradientStyle}
    >
      {children}
    </div>
  );
}
