@import "tailwindcss";
@import "./styles/mobile-map.css";

@custom-variant dark (&:is(.dark *));

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 222.2 84% 4.9%;

        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;

        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;

        --primary: 246 80% 60%;
        --primary-foreground: 0 0% 100%;

        --secondary: 210 40% 96.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;

        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;

        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;

        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;

        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 222.2 84% 4.9%;

        --radius: 0.5rem;

        /* Enhanced contrast colors for better accessibility */
        --location-title-color: #dc2626;
        --location-text-color: #111827;
        --location-coord-color: #4b5563;

        /* Status colors with better contrast */
        --success: 142 76% 36%;
        --success-foreground: 0 0% 100%;
        --warning: 38 92% 50%;
        --warning-foreground: 0 0% 100%;
        --error: 0 84% 60%;
        --error-foreground: 0 0% 100%;

        /* Table and interactive element colors */
        --table-header: 210 40% 98%;
        --table-row-hover: 210 40% 96%;
        --sidebar-active: 246 80% 60%;
        --sidebar-active-foreground: 0 0% 100%;
    }

    .dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;

        --card: 217.2 32.6% 17.5%;
        --card-foreground: 210 40% 98%;

        --popover: 217.2 32.6% 17.5%;
        --popover-foreground: 210 40% 98%;

        --primary: 246 80% 60%;
        --primary-foreground: 0 0% 100%;

        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;

        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;

        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;

        --destructive: 0 62.8% 50%;
        --destructive-foreground: 210 40% 98%;

        --border: 217.2 32.6% 17.5%;
        --input: 217.2 32.6% 17.5%;
        --ring: 212.7 26.8% 83.9%;

        /* Enhanced dark mode colors for better contrast */
        --location-title-color: #f87171;
        --location-text-color: #f3f4f6;
        --location-coord-color: #d1d5db;

        /* Dark mode status colors with better contrast */
        --success: 142 76% 45%;
        --success-foreground: 0 0% 100%;
        --warning: 38 92% 60%;
        --warning-foreground: 0 0% 100%;
        --error: 0 84% 65%;
        --error-foreground: 0 0% 100%;

        /* Dark mode table and interactive element colors */
        --table-header: 217.2 32.6% 15%;
        --table-row-hover: 217.2 32.6% 20%;
        --sidebar-active: 246 80% 60%;
        --sidebar-active-foreground: 0 0% 100%;
    }
}

/* Base styles */
html {
    scroll-behavior: smooth;
}

body {
    background-color: #ffffff;
    color: #0f172a;
}

/* Custom utilities */
.container {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding-right: 1rem;
    padding-left: 1rem;
}

/* Responsive container */
@media (min-width: 640px) {
    .container {
        max-width: 640px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 768px;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
    }
}

@media (min-width: 1280px) {
    .container {
        max-width: 1280px;
    }
}

/* Custom components */
.btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #0f172a;
    color: white;
}

.btn-secondary {
    background-color: #64748b;
    color: white;
}

/* Prevent swipe-to-go-back gesture on mobile */
body {
    overscroll-behavior-x: none;
    touch-action: pan-y pinch-zoom;
}

/* Prevent accidental navigation on user dashboard */
.user-dashboard-container {
    touch-action: pan-y pinch-zoom;
    overscroll-behavior-x: none;
}

/* Custom Map Components */
.maplibregl-popup.custom-popup {
    animation: popup-fade-in 0.3s ease-out;
}

.maplibregl-popup.custom-popup .maplibregl-popup-content {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(239, 68, 68, 0.2);
    padding: 8px;
    background: white;
}

[data-theme="dark"] .maplibregl-popup.custom-popup .maplibregl-popup-content,
.dark .maplibregl-popup.custom-popup .maplibregl-popup-content {
    background: #1e293b;
    color: #e2e8f0;
    border-color: rgba(239, 68, 68, 0.4);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.maplibregl-popup.custom-popup .maplibregl-popup-tip {
    border-top-color: rgba(239, 68, 68, 0.8);
}

[data-theme="dark"] .maplibregl-popup.custom-popup .maplibregl-popup-tip,
.dark .maplibregl-popup.custom-popup .maplibregl-popup-tip {
    border-top-color: #1e293b;
}

@keyframes popup-fade-in {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@theme inline {
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

:root {
    --radius: 0.625rem;
    --background: oklch(1 0 0);
    --foreground: oklch(0.129 0.042 264.695);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.129 0.042 264.695);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.129 0.042 264.695);
    --primary: oklch(0.6 0.22 280);
    --primary-foreground: oklch(0.984 0.003 247.858);
    --secondary: oklch(0.968 0.007 247.896);
    --secondary-foreground: oklch(0.208 0.042 265.755);
    --muted: oklch(0.968 0.007 247.896);
    --muted-foreground: oklch(0.554 0.046 257.417);
    --accent: oklch(0.968 0.007 247.896);
    --accent-foreground: oklch(0.208 0.042 265.755);
    --destructive: oklch(0.577 0.245 27.325);
    --border: oklch(0.929 0.013 255.508);
    --input: oklch(0.929 0.013 255.508);
    --ring: oklch(0.704 0.04 256.788);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.984 0.003 247.858);
    --sidebar-foreground: oklch(0.129 0.042 264.695);
    --sidebar-primary: oklch(0.7 0.15 230);
    --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
    --sidebar-accent: oklch(0.968 0.007 247.896);
    --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
    --sidebar-border: oklch(0.929 0.013 255.508);
    --sidebar-ring: oklch(0.704 0.04 256.788);

    --location-title-color: #ef4444;
    --location-text-color: #1f2937;
    --location-coord-color: #666666;
}

.dark {
    --background: oklch(0.129 0.042 264.695);
    --foreground: oklch(0.984 0.003 247.858);
    --card: oklch(0.208 0.042 265.755);
    --card-foreground: oklch(0.984 0.003 247.858);
    --popover: oklch(0.208 0.042 265.755);
    --popover-foreground: oklch(0.984 0.003 247.858);
    --primary: oklch(0.929 0.013 255.508);
    --primary-foreground: oklch(0.208 0.042 265.755);
    --secondary: oklch(0.279 0.041 260.031);
    --secondary-foreground: oklch(0.984 0.003 247.858);
    --muted: oklch(0.279 0.041 260.031);
    --muted-foreground: oklch(0.704 0.04 256.788);
    --accent: oklch(0.279 0.041 260.031);
    --accent-foreground: oklch(0.984 0.003 247.858);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.551 0.027 264.364);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.208 0.042 265.755);
    --sidebar-foreground: oklch(0.984 0.003 247.858);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
    --sidebar-accent: oklch(0.279 0.041 260.031);
    --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.551 0.027 264.364);

    --location-title-color: #f87171;
    --location-text-color: #e5e7eb;
    --location-coord-color: #9ca3af;
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/* MapLibre Marker styles */
.marker {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.marker svg {
    fill: white;
    stroke: #ef4444;
    stroke-width: 2;
}

/* Tambahkan styling yang lebih kuat untuk map container */
#map-container {
    width: 100% !important;
    height: 400px !important;
    position: relative !important;
    z-index: 1;
}

/* Force MapLibre styling */
.maplibregl-map {
    width: 100% !important;
    height: 100% !important;
}

.maplibregl-canvas-container {
    width: 100% !important;
    height: 100% !important;
}

.maplibregl-canvas {
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
}

.maplibregl-marker {
    cursor: pointer;
}

.maplibregl-ctrl-attrib-inner {
    font-size: 10px;
}

/* Custom Shadows */
.shadow-card {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

[data-theme="dark"] .shadow-card {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.36);
}

/* Button Styles */
.btn-primary {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded transition-colors;
}

[data-theme="dark"] .btn-primary {
    @apply bg-green-500 hover:bg-green-600;
}

/* Modern Table Scroll Styles */
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* Enhanced contrast utilities */
.text-high-contrast {
    color: hsl(var(--foreground));
}

.text-medium-contrast {
    color: hsl(var(--muted-foreground));
}

.bg-high-contrast {
    background-color: hsl(var(--background));
}

.bg-medium-contrast {
    background-color: hsl(var(--muted));
}

/* Status color utilities with proper contrast */
.status-success {
    background-color: hsl(var(--success));
    color: hsl(var(--success-foreground));
}

.status-warning {
    background-color: hsl(var(--warning));
    color: hsl(var(--warning-foreground));
}

.status-error {
    background-color: hsl(var(--error));
    color: hsl(var(--error-foreground));
}

/* Interactive element improvements */
.interactive-hover {
    transition: all 0.2s ease-in-out;
}

.interactive-hover:hover {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
}

/* Focus improvements for accessibility */
.focus-visible-enhanced:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    border-radius: 4px;
}

/* Table improvements */
.table-header-enhanced {
    background-color: hsl(var(--table-header));
    color: hsl(var(--muted-foreground));
    font-weight: 600;
}

.table-row-enhanced:hover {
    background-color: hsl(var(--table-row-hover));
}

/* Card improvements */
.card-enhanced {
    background-color: hsl(var(--card));
    color: hsl(var(--card-foreground));
    border: 1px solid hsl(var(--border));
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.dark .card-enhanced {
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
}

/* Header and navigation improvements */
.header-enhanced {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid hsl(var(--border));
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.dark .header-enhanced {
    background: rgba(15, 23, 42, 0.98);
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
}

/* Sidebar improvements */
.sidebar-enhanced {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-right: 1px solid hsl(var(--border));
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.dark .sidebar-enhanced {
    background: rgba(15, 23, 42, 0.95);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
}

/* Button improvements */
.button-enhanced {
    transition: all 0.2s ease-in-out;
    font-weight: 500;
}

.button-enhanced:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px 0 rgb(0 0 0 / 0.12), 0 2px 4px 0 rgb(0 0 0 / 0.08);
}

.dark .button-enhanced:hover {
    box-shadow: 0 4px 8px 0 rgb(0 0 0 / 0.25), 0 2px 4px 0 rgb(0 0 0 / 0.15);
}

/* Text shadow for better readability on gradients */
.text-shadow-sm {
    text-shadow: 0 1px 2px rgb(0 0 0 / 0.1);
}

.text-shadow {
    text-shadow: 0 1px 3px rgb(0 0 0 / 0.12), 0 1px 2px rgb(0 0 0 / 0.24);
}

/* Enhanced text shadows for dark mode */
.dark .drop-shadow-sm {
    filter: drop-shadow(0 1px 2px rgb(0 0 0 / 0.3));
}

.dark .drop-shadow-md {
    filter: drop-shadow(0 4px 3px rgb(0 0 0 / 0.4)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.3));
}

.dark .drop-shadow-lg {
    filter: drop-shadow(0 10px 8px rgb(0 0 0 / 0.4)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.3));
}

/* Header text improvements for dark mode */
.dark .header-text-enhanced {
    text-shadow: 0 2px 4px rgb(0 0 0 / 0.5), 0 1px 2px rgb(0 0 0 / 0.3);
    color: rgb(249 250 251);
}

.dark .header-subtitle-enhanced {
    text-shadow: 0 1px 3px rgb(0 0 0 / 0.4);
    color: rgb(229 231 235);
}

/* Gradient background improvements for dark mode */
.gradient-header-user {
    background: linear-gradient(to right, rgb(124 58 237), rgb(147 51 234), rgb(99 102 241)) !important;
}

.dark .gradient-header-user {
    background: linear-gradient(to right, rgb(76 29 149), rgb(88 28 135), rgb(55 48 163)) !important;
}

/* Force gradient for user dashboard header */
.user-dashboard-container .bg-gradient-to-r {
    background: linear-gradient(to right, rgb(124 58 237), rgb(147 51 234), rgb(99 102 241)) !important;
}

.dark .user-dashboard-container .bg-gradient-to-r {
    background: linear-gradient(to right, rgb(76 29 149), rgb(88 28 135), rgb(55 48 163)) !important;
}

/* Specific styling for user header gradient */
.user-header-gradient {
    background: linear-gradient(to right, #7c3aed, #9333ea, #6366f1) !important;
    background-color: #7c3aed !important;
    background-image: linear-gradient(to right, #7c3aed, #9333ea, #6366f1) !important;
}

.dark .user-header-gradient {
    background: linear-gradient(to right, #4c1d95, #581c87, #3730a3) !important;
    background-color: #4c1d95 !important;
    background-image: linear-gradient(to right, #4c1d95, #581c87, #3730a3) !important;
}

/* Override any conflicting styles with highest specificity */
div.user-header-gradient {
    background: linear-gradient(to right, #7c3aed, #9333ea, #6366f1) !important;
    background-color: #7c3aed !important;
    background-image: linear-gradient(to right, #7c3aed, #9333ea, #6366f1) !important;
}

.dark div.user-header-gradient {
    background: linear-gradient(to right, #4c1d95, #581c87, #3730a3) !important;
    background-color: #4c1d95 !important;
    background-image: linear-gradient(to right, #4c1d95, #581c87, #3730a3) !important;
}

/* Force override with even higher specificity */
.user-dashboard-container div.user-header-gradient {
    background: linear-gradient(to right, #7c3aed, #9333ea, #6366f1) !important;
    background-color: #7c3aed !important;
    background-image: linear-gradient(to right, #7c3aed, #9333ea, #6366f1) !important;
}

.dark .user-dashboard-container div.user-header-gradient {
    background: linear-gradient(to right, #4c1d95, #581c87, #3730a3) !important;
    background-color: #4c1d95 !important;
    background-image: linear-gradient(to right, #4c1d95, #581c87, #3730a3) !important;
}

.gradient-header-admin {
    background: linear-gradient(to right, rgb(59 130 246), rgb(37 99 235), rgb(99 102 241));
}

.dark .gradient-header-admin {
    background: linear-gradient(to right, rgb(30 64 175), rgb(29 78 216), rgb(67 56 202));
}

/* Enhanced contrast for dark mode headers */
.dark .header-container {
    border-bottom: 1px solid rgb(139 92 246 / 0.3);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
}

/* Gradient text improvements */
.gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary)) 50%, hsl(var(--accent-foreground)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Smooth scroll behavior for tables */
.modern-table-scroll {
    scroll-behavior: smooth;
}

/* Enhanced focus styles for accessibility */
.modern-table-scroll:focus-within {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
}