"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils/cn";
import { Button } from "@/app/components/ui/button";
import { LuMenu, LuLayoutDashboard as LuDashboard, LuShoppingCart, LuUser, LuCreditCard, LuPackage, LuPower } from "react-icons/lu";
import { ScrollArea } from "@/app/components/ui/scroll-area";
import { She<PERSON>, Sheet<PERSON>ontent, Sheet<PERSON>rigger, SheetTitle } from "@/app/components/ui/sheet";
import { useEffect, useState } from "react";
import { MobileBottomNavigation } from "@/app/components/shared/mobile-bottom-navigation";
import { NotificationBadge } from "@/app/components/shared/notification/notification-badge";
import { Bell } from "lucide-react";
import { ThemeToggle } from "@/app/components/ui/theme-toggle";


interface UserLayoutProps {
  children: React.ReactNode;
}

interface UserData {
  id?: string;
  name?: string;
  email?: string;
  image?: string | null;
}

const navigation = [
  {
    name: "Dashboard",
    href: "/user/dashboard",
    icon: LuDashboard,
    description: "Ikhtisar dan statistik penyewaan",
    badge: null,
    color: "blue"
  },
  {
    name: "Katalog",
    href: "/user/catalog",
    icon: LuPackage,
    description: "Lihat produk dan peralatan tersedia",
    badge: "Populer",
    color: "emerald"
  },
  {
    name: "Rental Saya",
    href: "/user/rentals",
    icon: LuShoppingCart,
    description: "Kelola penyewaan aktif dan riwayat",
    badge: null,
    color: "violet"
  },
  {
    name: "Status Operasi",
    href: "/user/operations",
    icon: LuPower,
    description: "Pantau status genset yang beroperasi",
    badge: null,
    color: "orange"
  },
  {
    name: "Pembayaran",
    href: "/user/payments",
    icon: LuCreditCard,
    description: "Invoice dan riwayat pembayaran",
    badge: null,
    color: "indigo"
  },
  {
    name: "Profil",
    href: "/user/profile",
    icon: LuUser,
    description: "Pengaturan akun dan preferensi",
    badge: null,
    color: "slate"
  },
];

export default function UserLayout({ children }: UserLayoutProps) {
  const pathname = usePathname();
  const [user, setUser] = useState<UserData | null>(null);
  const [fetchError, setFetchError] = useState(false);


  useEffect(() => {
    // Fetch user data from server
    const fetchUserData = async () => {
      try {
        // Use relative URL to automatically handle base path
        const controller = new AbortController();
        const signal = controller.signal;

        const response = await fetch('/api/users/me', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          },
          signal
        }).catch(err => {
          console.error("Network error in fetchUserData:", err);
          setFetchError(true);
          return null;
        });

        if (response && response.ok) {
          const userData = await response.json();
          setUser(userData);
          setFetchError(false);
        } else if (response) {
          console.error("Error fetching user data, status:", response.status);
          setFetchError(true);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        setFetchError(true);
      }
    };

    try {
      fetchUserData();
    } catch (e) {
      console.error("Fatal error in fetchUserData:", e);
      setFetchError(true);
    }

    // Cleanup function
    return () => {
      // Nothing to clean up since we removed the timeout
    };
  }, []);

  // Get page title based on current pathname
  const getPageTitle = () => {
    const currentNav = navigation.find(item =>
      pathname === item.href || pathname.startsWith(`${item.href}/`)
    );
    return currentNav?.name || "Dashboard Pengguna";
  };

  // User profile component
  const UserProfile = () => (
    <div className="border-t dark:border-gray-700 pt-4 mt-4 px-4">
      <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
        <div className="flex items-center gap-3 mb-3">
          {user?.image ? (
            <div className="w-10 h-10 rounded-full overflow-hidden ring-2 ring-white dark:ring-gray-700 shadow-sm">
              <Image
                src={user.image}
                alt={user.name || "Profil Pengguna"}
                width={40}
                height={40}
                className="w-full h-full object-cover"
              />
            </div>
          ) : (
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-violet-500 to-indigo-600 dark:from-violet-600 dark:to-indigo-700 flex items-center justify-center shadow-sm">
              <span className="text-white font-medium">
                {user?.name?.[0] || "U"}
              </span>
            </div>
          )}
          <div className="flex-1 truncate">
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{user?.name || 'Pengguna'}</p>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{user?.email || '<EMAIL>'}</p>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <ThemeToggle />
          <div className="flex gap-1">
            {fetchError ? (
              <Button variant="ghost" size="icon" className="relative text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full w-8 h-8">
                <Bell className="h-4 w-4" />
              </Button>
            ) : (
              <NotificationBadge />
            )}
            <Link href="/user/profile">
              <Button variant="ghost" size="icon" className="text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full w-8 h-8">
                <LuUser className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex min-screen bg-gradient-to-br from-violet-50 via-white to-indigo-50/30 dark:from-violet-950 dark:via-slate-900 dark:to-indigo-950/30 overflow-hidden user-dashboard-container">
      {/* Sidebar untuk desktop */}
      <aside className="hidden lg:flex lg:w-80 lg:flex-col lg:fixed lg:inset-y-0 z-50">
        <div className="flex flex-col flex-1 min-h-0 bg-white/95 backdrop-blur-xl border-r border-violet-200/60 shadow-xl dark:bg-slate-900/95 dark:border-violet-700/60">
          {/* Header dengan logo modern */}
          <div className="flex items-center h-20 px-6 border-b border-slate-200/60 dark:border-slate-700/60 shadow-lg user-header-gradient-inline">
            <Link href="/user/dashboard" className="flex items-center gap-3 group">
              <div className="w-12 h-12 bg-white/25 backdrop-blur-sm rounded-2xl flex items-center justify-center group-hover:bg-white/35 transition-all duration-300 group-hover:scale-105 shadow-lg dark:bg-gray-100/20 dark:group-hover:bg-gray-100/30">
                <span className="text-2xl font-bold text-white drop-shadow-md dark:text-gray-50 dark:drop-shadow-lg">RG</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-white drop-shadow-md dark:text-gray-50 dark:drop-shadow-lg">RentalGenset</span>
                <span className="text-xs text-white/95 font-semibold dark:text-gray-100 dark:font-bold">Dashboard Pengguna</span>
              </div>
            </Link>
          </div>
          <ScrollArea className="flex-1 py-6">
            {/* Search Bar */}
            <div className="px-4 mb-8">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-violet-400 dark:text-violet-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Cari menu atau fitur..."
                  className="block w-full pl-12 pr-4 py-3 bg-violet-50/50 border border-violet-200/60 rounded-xl text-sm placeholder-violet-400 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-violet-500 transition-all duration-200 dark:bg-violet-900/20 dark:border-violet-700/60 dark:text-violet-100 dark:placeholder-violet-400 dark:focus:ring-violet-400"
                />
              </div>
            </div>

            {/* Navigation Section */}
            <div className="px-4">
              <div className="mb-6">
                <h3 className="text-xs font-semibold text-violet-500 dark:text-violet-400 uppercase tracking-wider mb-4">
                  Menu Utama
                </h3>
                <nav className="space-y-2">
                  {navigation.map((item) => {
                    const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                          "group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200",
                          isActive
                            ? "bg-violet-50 text-violet-600 dark:bg-violet-900/30 dark:text-violet-400"
                            : "text-gray-700 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-gray-100"
                        )}
                      >
                        <div className={cn(
                          "mr-3 rounded-md p-2",
                          isActive ? "bg-violet-100 dark:bg-violet-900/50" : "bg-gray-100 dark:bg-gray-700"
                        )}
                        >
                          <item.icon
                            className={cn(
                              "h-5 w-5",
                              isActive ? "text-violet-600 dark:text-violet-400" : "text-gray-500 dark:text-gray-400"
                            )}
                          />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span>{item.name}</span>
                            {item.badge && (
                              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-violet-100 text-violet-700 dark:bg-violet-900/30 dark:text-violet-300">
                                {item.badge}
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">{item.description}</p>
                        </div>
                      </Link>
                    );
                  })}
                </nav>
              </div>
            </div>
          </ScrollArea>
          {user && <UserProfile />}
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="lg:hidden fixed top-4 left-4 z-50 h-11 w-11 bg-white/90 backdrop-blur-sm border border-violet-200 shadow-lg hover:bg-white text-violet-600 hover:text-violet-700 dark:bg-slate-800/90 dark:border-violet-700 dark:text-violet-400 dark:hover:bg-slate-800 dark:hover:text-violet-300 rounded-xl">
            <LuMenu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-80 border-r-0 bg-white/95 backdrop-blur-xl dark:bg-slate-900/95 dark:border-slate-700">
          <SheetTitle className="sr-only">Menu Navigasi Pengguna</SheetTitle>
          <div className="flex flex-col min-h-screen">
            <div className="flex items-center h-20 px-6 border-b border-violet-200/60 dark:border-violet-700/60 shadow-lg user-header-gradient-inline">
              <Link href="/user/dashboard" className="flex items-center gap-3">
                <div className="w-10 h-10 bg-white/25 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg dark:bg-gray-100/20">
                  <span className="text-xl font-bold text-white drop-shadow-md dark:text-gray-50 dark:drop-shadow-lg">RG</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-xl font-bold text-white drop-shadow-md dark:text-gray-50 dark:drop-shadow-lg">RentalGenset</span>
                  <span className="text-xs text-white/95 font-semibold dark:text-gray-100 dark:font-bold">Dashboard Pengguna</span>
                </div>
              </Link>
            </div>
            <ScrollArea className="flex-1 py-4">
              <nav className="space-y-1 px-2">
                {navigation.map((item) => {
                  const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        "group flex items-center px-3 py-2 text-sm font-medium rounded-md",
                        isActive
                          ? "bg-violet-50 text-violet-600 dark:bg-violet-900/30 dark:text-violet-400"
                          : "text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-gray-100"
                      )}
                    >
                      <item.icon
                        className={cn(
                          "mr-3 h-5 w-5 flex-shrink-0",
                          isActive ? "text-violet-600 dark:text-violet-400" : "text-gray-500 dark:text-gray-400"
                        )}
                      />
                      {item.name}
                    </Link>
                  );
                })}
              </nav>
            </ScrollArea>
            {user && <UserProfile />}
          </div>
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <div className="flex flex-col flex-1 lg:pl-80">
        <header className="sticky top-0 z-40 bg-white/98 backdrop-blur-xl border-b border-violet-200/60 dark:bg-slate-900/98 dark:border-violet-700/60 shadow-lg">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden h-10 w-10 bg-violet-100 hover:bg-violet-200 text-violet-700 border border-violet-300 rounded-xl dark:bg-violet-800/50 dark:hover:bg-violet-700/50 dark:text-violet-200 dark:border-violet-600 shadow-sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  // Cek apakah ada halaman sebelumnya dalam history
                  if (window.history.length > 1) {
                    window.history.back();
                  } else {
                    // Jika tidak ada history, redirect ke dashboard
                    window.location.href = '/user/dashboard';
                  }
                }}
                onTouchStart={(e) => {
                  // Prevent accidental touches
                  e.stopPropagation();
                }}
              >
                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4">
                  <path d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                </svg>
              </Button>
              <div className="flex flex-col">
                <div className="font-bold text-lg text-slate-900 dark:text-slate-100">RentalGenset</div>
                <p className="text-xs text-violet-600 dark:text-violet-400 font-semibold">
                  {getPageTitle()}
                </p>
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center gap-3">
              <div className="hidden lg:flex items-center gap-2">
                <ThemeToggle />
                {fetchError ? (
                  <Button variant="ghost" size="icon" className="relative text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800">
                    <Bell className="h-5 w-5" />
                  </Button>
                ) : (
                  <NotificationBadge />
                )}
              </div>

              {/* Mobile user info */}
              {user && (
                <div className="lg:hidden flex items-center gap-2">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-violet-500 to-purple-600 flex items-center justify-center shadow-sm">
                    <span className="text-white font-bold text-sm">
                      {user.name?.[0] || "U"}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <ThemeToggle />
                    {fetchError ? (
                      <Button variant="ghost" size="icon" className="relative text-slate-700 dark:text-slate-300">
                        <Bell className="h-5 w-5" />
                      </Button>
                    ) : (
                      <NotificationBadge />
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </header>
        <main className="flex-1 overflow-y-auto pb-24 md:pb-10" style={{ touchAction: 'pan-y pinch-zoom' }}>
          <div className="px-4 sm:px-6 lg:px-8 py-6 max-w-7xl mx-auto">
            {children}
          </div>
        </main>

        {/* Mobile Bottom Navigation */}
        <MobileBottomNavigation />
      </div>
    </div>
  );
}
